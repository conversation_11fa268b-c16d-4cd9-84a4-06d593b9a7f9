@import url(https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700;800&display=swap);
/* Codibu Design - EXACT Wix Studio Copy */

/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Wix Madefor Display', Arial, sans-serif;
  line-height: 1.6;
  color: #000000;
  background-color: #ffffff;
  font-weight: 400;
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Header - Exact Wix Style */
.header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  padding: 15px 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  text-decoration: none;
}

.nav {
  display: none;
  gap: 30px;
}

.nav a {
  color: #000000;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.nav a:hover {
  color: #666666;
}

.menu-btn {
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #000000;
}

/* Mobile Menu */
.mobile-nav {
  position: fixed;
  top: 70px;
  left: 0;
  right: 0;
  background: white;
  padding: 20px;
  display: none;
  z-index: 999;
}

.mobile-nav.active {
  display: block;
}

.mobile-nav a {
  display: block;
  padding: 15px 0;
  color: #000000;
  text-decoration: none;
  border-bottom: 1px solid #eee;
}

/* Hero Section - Exact Wix Layout */
.hero {
  padding: 120px 0 80px;
  background: #ffffff;
}

.hero-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: start;
}

.hero-content h1 {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: #000000;
  margin-bottom: 30px;
}

.hero-content .subtitle {
  font-size: 18px;
  color: #000000;
  margin-bottom: 20px;
  line-height: 1.5;
}

.hero-content .description {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 40px;
}

/* Services Sidebar - Right Side */
.services-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #eee;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
}

.service-item:hover {
  background: #f8f9fa;
  padding-left: 15px;
}

.service-number {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-right: 20px;
  min-width: 40px;
}

.service-content h3 {
  font-size: 16px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

.service-content p {
  font-size: 14px;
  color: #666666;
}

/* About Section */
.about {
  padding: 80px 0;
  background: #ffffff;
}

.about-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: start;
}

.about h2 {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 30px;
}

.about h3 {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 20px;
}

.about p {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.about ul {
  list-style: none;
  padding: 0;
}

.about ul li {
  font-size: 16px;
  color: #666666;
  margin-bottom: 10px;
  line-height: 1.6;
}

/* Portfolio Section */
.portfolio {
  padding: 80px 0;
  background: #ffffff;
}

.portfolio-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.portfolio h2 {
  font-size: 36px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 50px;
}

.portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
}

.portfolio-item {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 8px;
}

.portfolio-item h3 {
  font-size: 20px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 15px;
}

.portfolio-item p {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
}

/* Footer */
.footer {
  padding: 80px 0 40px;
  background: #f8f9fa;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 60px;
}

.footer h3 {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  margin-bottom: 20px;
}

.footer h4 {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 15px;
}

.footer p {
  font-size: 16px;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.footer ul {
  list-style: none;
  padding: 0;
}

.footer ul li {
  margin-bottom: 10px;
}

.footer ul li a {
  color: #666666;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.footer ul li a:hover {
  color: #000000;
}

.footer-bottom {
  border-top: 1px solid #ddd;
  padding-top: 30px;
  margin-top: 40px;
  text-align: center;
  color: #666666;
  font-size: 14px;
}

/* Responsive Design */
@media (min-width: 768px) {
  .nav {
    display: flex;
  }

  .menu-btn {
    display: none;
  }
}

@media (max-width: 1024px) {
  .hero-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .about-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .footer-container {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .hero-content h1 {
    font-size: 36px;
  }

  .about h2,
  .portfolio h2 {
    font-size: 28px;
  }

  .portfolio-grid {
    grid-template-columns: 1fr;
  }
}

/* Main Hero Section - Exact Wix Studio Style */
.wix-hero {
  min-height: 100vh;
  background-color: #000000;
  position: relative;
  display: flex;
  align-items: center;
  padding: 0 40px;
}

.wix-hero-container {
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 80px;
  align-items: center;
}

.wix-hero-content {
  padding-right: 40px;
}

.wix-hero-title {
  font-size: 72px;
  font-weight: 800;
  color: #ffffff;
  line-height: 0.9;
  margin-bottom: 40px;
  letter-spacing: -2px;
}

.wix-hero-subtitle {
  font-size: 24px;
  color: #ffffff;
  line-height: 1.4;
  margin-bottom: 30px;
  font-weight: 400;
  opacity: 0.9;
}

.wix-hero-description {
  font-size: 18px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 50px;
  max-width: 500px;
}

/* Numbered Services List - Right Side */
.wix-services-sidebar {
  display: flex;
  flex-direction: column;
  gap: 0;
}

.wix-service-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #333333;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
}

.wix-service-item:hover {
  background-color: rgba(255, 255, 255, 0.05);
  padding-left: 20px;
}

.wix-service-number {
  font-size: 24px;
  font-weight: 700;
  color: #ffffff;
  margin-right: 30px;
  min-width: 40px;
}

.wix-service-content {
  flex: 1;
}

.wix-service-title {
  font-size: 18px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 5px;
}

.wix-service-desc {
  font-size: 14px;
  color: #999999;
  line-height: 1.4;
}

/* About Section - Wix Studio Style */
.wix-about {
  padding: 100px 40px;
  background-color: #111111;
}

.wix-about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: start;
}

.wix-about-content {
  padding-right: 40px;
}

.wix-about-title {
  font-size: 48px;
  font-weight: 700;
  color: #ffffff;
  margin-bottom: 30px;
  letter-spacing: -1px;
  line-height: 1.1;
}

.wix-about-subtitle {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 25px;
}

.wix-about-text {
  font-size: 16px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 40px;
}

.wix-features-title {
  font-size: 24px;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 25px;
}

.wix-features-list {
  list-style: none;
  padding: 0;
}

.wix-features-list li {
  font-size: 16px;
  color: #cccccc;
  margin-bottom: 15px;
  line-height: 1.6;
}

.wix-features-list li strong {
  color: #ffffff;
}

.wix-about-visual {
  background: linear-gradient(135deg, #333333 0%, #666666 100%);
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 18px;
  font-weight: 500;
}

/* Portfolio Section - Wix Studio Style */
.wix-portfolio {
  padding: 100px 40px;
  background-color: #000000;
}

.wix-portfolio-container {
  max-width: 1200px;
  margin: 0 auto;
}

.wix-portfolio-title {
  font-size: 48px;
  font-weight: 700;
  color: #ffffff;
  text-align: center;
  margin-bottom: 20px;
  letter-spacing: -1px;
}

.wix-portfolio-subtitle {
  font-size: 18px;
  color: #cccccc;
  text-align: center;
  margin-bottom: 80px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.wix-portfolio-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.wix-portfolio-item {
  position: relative;
  height: 400px;
  overflow: hidden;
  background: #222222;
  transition: all 0.3s ease;
}

.wix-portfolio-item:hover {
  transform: translateY(-10px);
}

.wix-portfolio-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0.3) 100%);
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40px;
  transition: all 0.3s ease;
}

.wix-portfolio-item:hover .wix-portfolio-overlay {
  background: linear-gradient(135deg, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 100%);
}

.wix-portfolio-item-title {
  font-size: 24px;
  font-weight: 600;
  color: white;
  margin-bottom: 10px;
}

.wix-portfolio-item-description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

/* Footer - Wix Studio Style */
.wix-footer {
  padding: 100px 40px 60px;
  background-color: #111111;
  color: white;
}

.wix-footer-container {
  max-width: 1200px;
  margin: 0 auto;
}

.wix-footer-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
}

.wix-footer-title {
  font-size: 32px;
  font-weight: 700;
  color: white;
  margin-bottom: 30px;
  letter-spacing: -1px;
}

.wix-footer-text {
  font-size: 16px;
  color: #cccccc;
  line-height: 1.6;
  margin-bottom: 30px;
}

.wix-footer-section-title {
  font-size: 18px;
  font-weight: 600;
  color: white;
  margin-bottom: 20px;
}

.wix-footer-nav {
  list-style: none;
  padding: 0;
}

.wix-footer-nav li {
  margin-bottom: 12px;
}

.wix-footer-nav a {
  color: #cccccc;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s ease;
}

.wix-footer-nav a:hover {
  color: white;
}

.wix-footer-bottom {
  border-top: 1px solid #333333;
  padding-top: 40px;
  text-align: center;
}

.wix-footer-services {
  color: #999999;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 20px;
}

.wix-footer-copyright {
  color: #666666;
  font-size: 14px;
}

/* Responsive Design - Wix Studio Style */
@media (min-width: 768px) {
  .wix-nav {
    display: flex;
  }

  .wix-menu-btn {
    display: none;
  }
}

@media (max-width: 1024px) {
  .wix-hero {
    padding: 0 20px;
  }

  .wix-hero-container {
    grid-template-columns: 1fr;
    gap: 60px;
    text-align: center;
  }

  .wix-hero-content {
    padding-right: 0;
  }

  .wix-hero-title {
    font-size: 56px;
  }

  .wix-about-container {
    grid-template-columns: 1fr;
    gap: 60px;
    padding: 0 20px;
  }

  .wix-about-content {
    padding-right: 0;
  }

  .wix-portfolio {
    padding: 80px 20px;
  }

  .wix-footer {
    padding: 80px 20px 40px;
  }

  .wix-footer-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
}

@media (max-width: 768px) {
  .wix-header {
    padding: 15px 20px;
  }

  .wix-hero-title {
    font-size: 42px;
  }

  .wix-hero-subtitle {
    font-size: 20px;
  }

  .wix-services-sidebar {
    margin-top: 40px;
  }

  .wix-about-title,
  .wix-portfolio-title {
    font-size: 36px;
  }

  .wix-portfolio-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .wix-portfolio-item {
    height: 300px;
  }
}

/* Special Wix-style elements */
.wix-numbered-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin: 40px 0;
}

.wix-numbered-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-left: 4px solid #000000;
}

.wix-numbered-item .number {
  font-size: 24px;
  font-weight: 700;
  color: #000000;
  min-width: 40px;
}

.wix-numbered-item .content {
  flex: 1;
}

.wix-numbered-item .title {
  font-size: 18px;
  font-weight: 600;
  color: #000000;
  margin-bottom: 5px;
}

.wix-numbered-item .description {
  font-size: 16px;
  color: #666666;
}

/* Additional mobile styles */
@media (max-width: 767px) {
  .wix-hero-title {
    font-size: 36px;
  }

  .wix-hero-buttons {
    flex-direction: column;
  }

  .wix-btn-primary,
  .wix-btn-secondary {
    text-align: center;
    width: 100%;
  }

  .wix-numbered-list {
    margin-top: 60px;
  }

  .wix-services-grid,
  .wix-portfolio-grid {
    grid-template-columns: 1fr;
  }

  .wix-about-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .wix-footer-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .wix-footer-grid > div[style*="grid-column"] {
    grid-column: span 1 !important;
  }
}
